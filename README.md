# My REST API

A REST API built with FastAPI and Python, providing a foundation for building scalable web services.

## Features

- **FastAPI Framework**: Modern, fast web framework for building APIs
- **Automatic Documentation**: Interactive API docs with Swagger UI
- **Type Safety**: Pydantic models for request/response validation
- **Hot Reload**: Development server with automatic reloading
- **CRUD Operations**: Complete Create, Read, Update, Delete functionality

## Requirements

- Python 3.12+
- uv (for dependency management)

## Installation

1. Clone or navigate to the project directory
2. Install dependencies using uv:
   ```bash
   uv sync
   ```

## Running the Application

### Development Mode

Run the application with hot reload:

```bash
uv run python main.py
```

Or using uvicorn directly:

```bash
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at:
- **API**: http://localhost:8000
- **Interactive Docs (Swagger UI)**: http://localhost:8000/docs
- **Alternative Docs (ReDoc)**: http://localhost:8000/redoc

## API Endpoints

### Basic Endpoints

- `GET /` - Welcome message
- `GET /health` - Health check

### Items CRUD

- `GET /items` - Get all items
- `GET /items/{item_id}` - Get item by ID
- `POST /items` - Create new item
- `PUT /items/{item_id}` - Update item
- `DELETE /items/{item_id}` - Delete item

## Example Usage

### Create an Item

```bash
curl -X POST "http://localhost:8000/items" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Sample Item",
       "description": "A sample item for testing",
       "price": 29.99,
       "is_available": true
     }'
```

### Get All Items

```bash
curl "http://localhost:8000/items"
```

### Get Item by ID

```bash
curl "http://localhost:8000/items/1"
```

## Project Structure

```
my-rest-api/
├── main.py          # FastAPI application
├── pyproject.toml   # Project configuration and dependencies
├── uv.lock         # Dependency lock file
└── README.md       # This file
```

## Development

### Adding Dependencies

Use uv to add new dependencies:

```bash
uv add package-name
```

### Code Structure

The application is structured with:
- **Pydantic Models**: For request/response validation
- **FastAPI Routes**: RESTful endpoints
- **In-memory Storage**: Simple list-based storage (replace with database for production)

## Next Steps

This is a basic foundation. Consider adding:

- Database integration (PostgreSQL, SQLite, etc.)
- Authentication and authorization
- Logging and monitoring
- Testing suite
- Docker containerization
- Environment configuration
- Error handling middleware
- Rate limiting
- CORS configuration

## License

This project is open source and available under the MIT License.